#!/usr/bin/env python3
"""
MINE数据集数据读取和展示脚本
展示项目如何读取和处理多模态数据
"""

import os
import json
import numpy as np
import pandas as pd
from collections import Counter
import matplotlib.pyplot as plt
import seaborn as sns

def load_dataset_info():
    """加载数据集基本信息"""
    
    # 标签定义
    goal_labels = [
        'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
        'Agree', 'Taunt', 'Flaunt', 'Joke', 'Oppose',
        'Comfort', 'Care', 'Inform', 'Advise', 'Arrange', 
        'Introduce', 'Leave', 'Prevent', 'Greet', 'Ask for help', 'other'
    ]
    
    emotion_labels = [
        'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
        'Agree', 'Taunt', 'Flaunt', 'Joke', 'Oppose', 'Comfort'
    ]
    
    # 特征维度信息
    feat_dims = {
        'text': 768,      # BERT特征
        'video': 256,     # Video Swin Transformer特征  
        'image': 256,     # ViT特征
        'audio': 768      # wav2vec 2.0特征
    }
    
    # 序列长度限制
    max_seq_lengths = {
        'text': 30,
        'video': 230, 
        'audio': 480
    }
    
    return goal_labels, emotion_labels, feat_dims, max_seq_lengths

def load_split_data():
    """加载训练/验证/测试数据分割"""
    
    data_path = "CVPR25-MINE/mine-dataset"
    
    splits = {}
    for split in ['train', 'dev', 'test']:
        file_path = os.path.join(data_path, f"{split}_new_update.json")
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                splits[split] = json.load(f)
        else:
            print(f"警告: 找不到文件 {file_path}")
    
    return splits

def load_features():
    """加载预提取的特征"""
    
    data_path = "CVPR25-MINE/mine-dataset"
    features = {}
    
    # 加载各模态特征
    feature_files = {
        'text': 'text_feats_final.npy',
        'video': 'video_feats_final.npy', 
        'image': 'image_feats_final.npy',
        'audio': 'audio_feats_final.npy'
    }
    
    for modality, filename in feature_files.items():
        file_path = os.path.join(data_path, filename)
        if os.path.exists(file_path):
            try:
                feat = np.load(file_path, allow_pickle=True)
                features[modality] = feat
                print(f"{modality}特征加载成功: 形状 {feat.shape}, 数据类型 {feat.dtype}")
            except Exception as e:
                print(f"加载{modality}特征时出错: {e}")
        else:
            print(f"警告: 找不到特征文件 {file_path}")
    
    return features

def analyze_data_distribution(splits, goal_labels, emotion_labels):
    """分析数据分布"""
    
    print("\n=== 数据分布分析 ===")
    
    for split_name, data in splits.items():
        print(f"\n{split_name.upper()}集:")
        print(f"  样本数量: {len(data)}")
        
        # 分析模态组合
        modality_combinations = []
        goal_counts = []
        emotion_counts = []
        
        for sample in data:
            # 模态组合 [text, video, image, audio]
            modality_combinations.append(tuple(sample['type']))
            goal_counts.extend(sample['goal'])
            emotion_counts.append(sample['emotion'])
        
        # 模态组合统计
        modality_counter = Counter(modality_combinations)
        print(f"  模态组合分布:")
        for combo, count in modality_counter.most_common():
            modalities = []
            if combo[0]: modalities.append("文本")
            if combo[1]: modalities.append("视频") 
            if combo[2]: modalities.append("图像")
            if combo[3]: modalities.append("音频")
            print(f"    {'+'.join(modalities)}: {count}个样本")
        
        # Goal标签分布
        goal_counter = Counter(goal_counts)
        print(f"  Goal标签分布 (前10):")
        for goal_id, count in goal_counter.most_common(10):
            print(f"    {goal_labels[goal_id]}: {count}")
        
        # Emotion标签分布
        emotion_counter = Counter(emotion_counts)
        print(f"  Emotion标签分布:")
        for emotion_id, count in emotion_counter.most_common():
            print(f"    {emotion_labels[emotion_id]}: {count}")

def show_sample_data(splits, features, goal_labels, emotion_labels):
    """展示样本数据"""
    
    print("\n=== 样本数据展示 ===")
    
    # 选择训练集的前几个样本
    train_data = splits.get('train', [])
    if not train_data:
        print("没有找到训练数据")
        return
    
    for i in range(min(3, len(train_data))):
        sample = train_data[i]
        print(f"\n样本 {i+1}:")
        print(f"  ID: {sample['id']}")
        
        # 模态信息
        modalities = []
        if sample['type'][0]: modalities.append("文本")
        if sample['type'][1]: modalities.append("视频")
        if sample['type'][2]: modalities.append("图像") 
        if sample['type'][3]: modalities.append("音频")
        print(f"  可用模态: {', '.join(modalities)}")
        
        # 标签信息
        emotion_label = emotion_labels[sample['emotion']]
        goal_labels_list = [goal_labels[g] for g in sample['goal']]
        print(f"  情感标签: {emotion_label}")
        print(f"  目标标签: {', '.join(goal_labels_list)}")
        
        # 特征信息（如果有的话）
        sample_idx = int(sample['id']) - 1  # 假设ID从1开始
        if sample_idx < len(features.get('text', [])):
            print(f"  特征维度:")
            for modality, feat in features.items():
                if sample_idx < len(feat):
                    if hasattr(feat[sample_idx], 'shape'):
                        print(f"    {modality}: {feat[sample_idx].shape}")
                    else:
                        print(f"    {modality}: {type(feat[sample_idx])}")

def explain_data_loading_process():
    """解释数据加载流程"""
    
    print("\n=== 数据加载流程说明 ===")
    print("""
1. 数据组织结构:
   - CVPR25-MINE/mine-dataset/: 数据集根目录
   - train_new_update.json: 训练集标注文件
   - dev_new_update.json: 验证集标注文件  
   - test_new_update.json: 测试集标注文件
   - *_feats_final.npy: 预提取的各模态特征文件

2. 标注文件格式:
   每个样本包含:
   - id: 样本唯一标识符
   - type: [1,0,1,1] 表示该样本包含的模态 (文本,视频,图像,音频)
   - emotion: 情感标签ID (0-10)
   - goal: 目标标签ID列表 (多标签分类)

3. 特征提取:
   - 文本: 使用BERT-base-uncased提取768维特征
   - 视频: 使用Video Swin Transformer提取256维特征
   - 图像: 使用ViT提取256维特征  
   - 音频: 使用wav2vec 2.0提取768维特征

4. 数据加载流程:
   a) DataManager读取标注文件和特征文件
   b) 根据数据分割创建训练/验证/测试集
   c) MMDataset类封装多模态数据
   d) DataLoader提供批量数据迭代

5. 数据预处理:
   - 序列填充: 将变长序列填充到固定长度
   - 模态对齐: 处理缺失模态的情况
   - 标签编码: 将标签转换为模型可用格式
    """)

def main():
    """主函数"""
    
    print("MINE数据集数据读取和展示")
    print("=" * 50)
    
    # 加载基本信息
    goal_labels, emotion_labels, feat_dims, max_seq_lengths = load_dataset_info()
    
    print(f"数据集包含 {len(goal_labels)} 个目标标签和 {len(emotion_labels)} 个情感标签")
    print(f"特征维度: {feat_dims}")
    print(f"最大序列长度: {max_seq_lengths}")
    
    # 加载数据分割
    splits = load_split_data()
    
    # 加载特征
    features = load_features()
    
    # 分析数据分布
    analyze_data_distribution(splits, goal_labels, emotion_labels)
    
    # 展示样本数据
    show_sample_data(splits, features, goal_labels, emotion_labels)
    
    # 解释数据加载流程
    explain_data_loading_process()

if __name__ == "__main__":
    main()
