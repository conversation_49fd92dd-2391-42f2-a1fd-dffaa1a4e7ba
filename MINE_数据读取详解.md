# MINE项目数据读取详解

## 项目概述

MINE (Multimodal Intention and Emotion Understanding in the Wild) 是一个多模态意图和情感理解数据集，包含超过20,000个社交媒体帖子，支持文本、图像、视频、音频四种模态。

## 数据集结构

### 1. 数据文件组织
```
CVPR25-MINE/mine-dataset/
├── train_new_update.json      # 训练集标注 (16,134样本)
├── dev_new_update.json        # 验证集标注 (2,017样本)  
├── test_new_update.json       # 测试集标注 (2,017样本)
├── text_feats_final.npy       # 文本特征 (BERT-768维)
├── video_feats_final.npy      # 视频特征 (Video Swin-512维)
├── image_feats_final.npy      # 图像特征 (ViT-768维)
└── audio_feats_final.npy      # 音频特征 (wav2vec-768维)
```

### 2. 标注文件格式
每个JSON文件包含样本列表，每个样本结构如下：
```json
{
    "id": 15356,                    // 样本唯一标识符
    "type": [1, 0, 1, 1],          // 模态可用性 [文本,视频,图像,音频]
    "emotion": 4,                   // 情感标签ID (0-10)
    "goal": [3, 16]                // 目标标签ID列表 (多标签)
}
```

### 3. 标签体系

**情感标签 (11个):**
- 0: Complain, 1: Praise, 2: Apologise, 3: Thank, 4: Criticize
- 5: Agree, 6: Taunt, 7: Flaunt, 8: Joke, 9: Oppose, 10: Comfort

**目标标签 (21个):**
- 0-10: 与情感标签相同
- 11: Care, 12: Inform, 13: Advise, 14: Arrange, 15: Introduce
- 16: Leave, 17: Prevent, 18: Greet, 19: Ask for help, 20: other

## 数据读取流程

### 1. DataManager类 (data/base.py)

DataManager是数据管理的核心类，负责：

```python
class DataManager:
    def __init__(self, args, logger_name='Multimodal Intent Recognition'):
        # 1. 加载标签配置
        self.benchmarks = benchmarks[args.dataset]  # 从__init__.py加载配置
        self.label_list = self.benchmarks["emotion_labels"] or self.benchmarks['goal_labels']
        
        # 2. 加载标注文件
        self.train_data_index, self.train_label_ids, self.train_modality_info = 
            self._get_indexes_annotations('train_new_update.json', args.data_mode, args)
        # 同样处理dev和test
        
        # 3. 加载特征文件
        self.unimodal_feats = self._get_unimodal_feats(args, self._get_attrs())
        
        # 4. 创建多模态数据集
        self.mm_data = self._get_multimodal_data(args)
        
        # 5. 创建数据加载器
        self.mm_dataloader = self._get_dataloader(args, self.mm_data)
```

### 2. 标注文件解析

```python
def _get_indexes_annotations(self, json_path, data_mode, args):
    data_index = []      # 样本ID列表
    label_ids = []       # 标签向量列表
    support_label_ids = [] # 模态可用性列表
    
    with open(json_path, 'r') as f:
        datas = json.load(f)
    
    for data in datas:
        data_index.append(int(data['id']))
        support_label_ids.append(data['type'])  # [1,0,1,1]
        
        if data_mode == 'emotion':
            # 单标签分类：one-hot编码
            emotion = torch.zeros(args.class_num_emotion)
            emotion[int(data['emotion'])] = 1
            label_ids.append(emotion)
            
        elif data_mode == 'goal':
            # 多标签分类：multi-hot编码
            intent = torch.zeros(args.class_num_intent)
            for i in data['goal']:
                intent[i] = 1
            label_ids.append(intent)
    
    return data_index, label_ids, support_label_ids
```

### 3. 特征文件加载

```python
def _get_unimodal_feats(self, args, attrs):
    # 加载预提取的特征文件
    text_feats = np.load('text_feats_final.npy', allow_pickle=True)    # (20168, 768)
    video_feats = np.load('video_feats_final.npy', allow_pickle=True)  # (20168, 512)
    image_feats = np.load('image_feats_final.npy', allow_pickle=True)  # (20168, 768)
    audio_feats = np.load('audio_feats_final.npy', allow_pickle=True)  # (20168, 60, 768)
    
    # 根据样本ID索引对应特征
    text_feats_train = [text_feats[idx] for idx in self.train_data_index]
    # 同样处理其他模态和数据集分割
    
    # 特殊处理：视频+图像特征融合
    # 将512维视频特征扩展到768维，然后与图像特征相加
    video_feats_train = np.concatenate(
        (video_feats_train, np.zeros((video_feats_train.shape[0], 256))), axis=1)
    video_feats_train = image_feats_train + video_feats_train
    
    return {
        'text': {'train': text_feats_train, 'dev': text_feats_dev, 'test': text_feats_test},
        'video': {'train': video_feats_train, 'dev': video_feats_dev, 'test': video_feats_test},
        'audio': {'train': audio_feats_train, 'dev': audio_feats_dev, 'test': audio_feats_test}
    }
```

### 4. MMDataset类 (data/mm_pre.py)

多模态数据集类封装数据访问：

```python
class MMDataset(Dataset):
    def __init__(self, label_ids, text_feats, video_feats, audio_feats, support_ids):
        self.label_ids = label_ids        # 标签向量
        self.support_ids = torch.tensor(support_ids)  # 模态可用性
        self.text_feats = torch.tensor(text_feats)    # 文本特征
        self.video_feats = torch.tensor(video_feats)  # 视频特征(已融合图像)
        self.audio_feats = torch.tensor(audio_feats)  # 音频特征
        
    def __getitem__(self, index):
        return {
            'label_ids': self.label_ids[index],      # 标签
            'text_feats': self.text_feats[index],    # 文本特征
            'video_feats': self.video_feats[index],  # 视频特征
            'audio_feats': self.audio_feats[index],  # 音频特征
            'support_ids': self.support_ids[index]   # 模态可用性
        }
```

### 5. DataLoader创建

```python
def _get_dataloader(self, args, data):
    train_dataloader = DataLoader(
        data['train'], 
        shuffle=True, 
        batch_size=args.train_batch_size, 
        num_workers=args.num_workers, 
        pin_memory=True
    )
    # 同样创建dev和test的DataLoader
    return {'train': train_dataloader, 'dev': dev_dataloader, 'test': test_dataloader}
```

## 数据特点分析

### 1. 模态分布
- **文本**: 100% (所有样本都有文本)
- **视频**: 61.6% (主要模态组合)
- **图像**: 19.3% 
- **音频**: 8.0% (最稀少的模态)

### 2. 模态组合
- **文本+视频**: 61.3% (最常见组合)
- **仅文本**: 19.4%
- **文本+图像**: 11.2%
- **文本+图像+音频**: 7.8%
- **四模态完整**: 0.2% (非常稀少)

### 3. 标签分布
- **情感**: 以Criticize(52%)和Apologise(28%)为主
- **目标**: 以Advise(20%)和Thank(19%)为主
- **多标签**: 平均每样本2.32个目标标签

## 关键技术点

### 1. 缺失模态处理
- 使用`support_ids`标记模态可用性
- 缺失模态用零向量填充
- 模型需要处理不完整的多模态输入

### 2. 特征预处理
- **文本**: BERT-base-uncased → 768维
- **视频**: Video Swin Transformer → 512维 → 扩展到768维
- **图像**: ViT → 768维
- **音频**: wav2vec 2.0 → 768维序列特征
- **融合**: 视频特征与图像特征相加

### 3. 标签编码
- **情感**: 单标签分类，one-hot编码
- **目标**: 多标签分类，multi-hot编码
- 支持情感和目标的联合学习

## 使用示例

```python
# 初始化数据管理器
data_manager = DataManager(args)

# 获取数据加载器
train_loader = data_manager.mm_dataloader['train']

# 迭代数据
for batch in train_loader:
    labels = batch['label_ids']          # 标签
    text = batch['text_feats']           # 文本特征
    video = batch['video_feats']         # 视频特征(已融合图像)
    audio = batch['audio_feats']         # 音频特征
    modality_mask = batch['support_ids'] # 模态可用性掩码
    
    # 模型训练...
```

这个数据读取系统的设计充分考虑了真实社交媒体数据的特点，特别是模态不完整性和多标签分类的需求，为多模态情感和意图理解提供了完整的数据基础设施。
