#!/usr/bin/env python3
"""
MINE数据集可视化脚本
创建数据分布的可视化图表
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Sim<PERSON>ei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载数据"""
    data_path = "CVPR25-MINE/mine-dataset"
    
    splits = {}
    for split in ['train', 'dev', 'test']:
        file_path = os.path.join(data_path, f"{split}_new_update.json")
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                splits[split] = json.load(f)
    
    goal_labels = [
        'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
        'Agree', 'Taunt', 'Flaunt', 'Joke', 'Oppose',
        'Comfort', 'Care', 'Inform', 'Advise', 'Arrange', 
        'Introduce', 'Leave', 'Prevent', 'Greet', 'Ask for help', 'other'
    ]
    
    emotion_labels = [
        'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
        'Agree', 'Taunt', 'Flaunt', 'Joke', 'Oppose', 'Comfort'
    ]
    
    return splits, goal_labels, emotion_labels

def plot_dataset_overview(splits):
    """绘制数据集概览"""
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 数据集大小分布
    split_sizes = {split: len(data) for split, data in splits.items()}
    
    axes[0].bar(split_sizes.keys(), split_sizes.values(), 
                color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    axes[0].set_title('Dataset Split Sizes', fontsize=14, fontweight='bold')
    axes[0].set_ylabel('Number of Samples')
    for i, (split, size) in enumerate(split_sizes.items()):
        axes[0].text(i, size + 100, str(size), ha='center', va='bottom', fontweight='bold')
    
    # 模态组合分布（仅训练集）
    train_data = splits['train']
    modality_combinations = []
    for sample in train_data:
        combo = tuple(sample['type'])
        modalities = []
        if combo[0]: modalities.append("Text")
        if combo[1]: modalities.append("Video") 
        if combo[2]: modalities.append("Image")
        if combo[3]: modalities.append("Audio")
        modality_combinations.append('+'.join(modalities))
    
    modality_counter = Counter(modality_combinations)
    top_modalities = dict(modality_counter.most_common(6))
    
    axes[1].barh(list(top_modalities.keys()), list(top_modalities.values()),
                color='skyblue')
    axes[1].set_title('Modality Combinations (Training Set)', fontsize=14, fontweight='bold')
    axes[1].set_xlabel('Number of Samples')
    
    plt.tight_layout()
    plt.savefig('dataset_overview.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_label_distributions(splits, goal_labels, emotion_labels):
    """绘制标签分布"""
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    
    # Goal标签分布（训练集）
    train_data = splits['train']
    goal_counts = []
    for sample in train_data:
        goal_counts.extend(sample['goal'])
    
    goal_counter = Counter(goal_counts)
    top_goals = dict(goal_counter.most_common(15))
    goal_names = [goal_labels[i] for i in top_goals.keys()]
    
    axes[0,0].barh(goal_names, list(top_goals.values()), color='lightcoral')
    axes[0,0].set_title('Top 15 Goal Labels Distribution (Training Set)', 
                       fontsize=14, fontweight='bold')
    axes[0,0].set_xlabel('Number of Samples')
    
    # Emotion标签分布（训练集）
    emotion_counts = [sample['emotion'] for sample in train_data]
    emotion_counter = Counter(emotion_counts)
    emotion_names = [emotion_labels[i] for i in emotion_counter.keys()]
    emotion_values = list(emotion_counter.values())
    
    axes[0,1].bar(emotion_names, emotion_values, color='lightgreen')
    axes[0,1].set_title('Emotion Labels Distribution (Training Set)', 
                       fontsize=14, fontweight='bold')
    axes[0,1].set_ylabel('Number of Samples')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # 跨数据集的Goal标签分布对比
    goal_data = []
    for split_name, data in splits.items():
        goal_counts = []
        for sample in data:
            goal_counts.extend(sample['goal'])
        goal_counter = Counter(goal_counts)
        
        for goal_id, count in goal_counter.items():
            goal_data.append({
                'Split': split_name.capitalize(),
                'Goal': goal_labels[goal_id],
                'Count': count
            })
    
    goal_df = pd.DataFrame(goal_data)
    top_goal_names = [goal_labels[i] for i in dict(Counter(goal_counts).most_common(10)).keys()]
    goal_df_filtered = goal_df[goal_df['Goal'].isin(top_goal_names)]
    
    goal_pivot = goal_df_filtered.pivot(index='Goal', columns='Split', values='Count').fillna(0)
    goal_pivot.plot(kind='bar', ax=axes[1,0], color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    axes[1,0].set_title('Top 10 Goal Labels Across Splits', fontsize=14, fontweight='bold')
    axes[1,0].set_ylabel('Number of Samples')
    axes[1,0].tick_params(axis='x', rotation=45)
    axes[1,0].legend()
    
    # 跨数据集的Emotion标签分布对比
    emotion_data = []
    for split_name, data in splits.items():
        emotion_counts = [sample['emotion'] for sample in data]
        emotion_counter = Counter(emotion_counts)
        
        for emotion_id, count in emotion_counter.items():
            emotion_data.append({
                'Split': split_name.capitalize(),
                'Emotion': emotion_labels[emotion_id],
                'Count': count
            })
    
    emotion_df = pd.DataFrame(emotion_data)
    emotion_pivot = emotion_df.pivot(index='Emotion', columns='Split', values='Count').fillna(0)
    emotion_pivot.plot(kind='bar', ax=axes[1,1], color=['#1f77b4', '#ff7f0e', '#2ca02c'])
    axes[1,1].set_title('Emotion Labels Across Splits', fontsize=14, fontweight='bold')
    axes[1,1].set_ylabel('Number of Samples')
    axes[1,1].tick_params(axis='x', rotation=45)
    axes[1,1].legend()
    
    plt.tight_layout()
    plt.savefig('label_distributions.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_feature_analysis():
    """绘制特征分析"""
    data_path = "CVPR25-MINE/mine-dataset"
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 文本特征分析
    try:
        text_feats = np.load(os.path.join(data_path, 'text_feats_final.npy'), allow_pickle=True)
        
        # 特征统计
        text_mean = np.mean(text_feats, axis=0)
        text_std = np.std(text_feats, axis=0)
        
        axes[0,0].hist(text_mean, bins=50, alpha=0.7, color='blue')
        axes[0,0].set_title('Text Features Mean Distribution', fontsize=12, fontweight='bold')
        axes[0,0].set_xlabel('Feature Value')
        axes[0,0].set_ylabel('Frequency')
        
        axes[0,1].hist(text_std, bins=50, alpha=0.7, color='red')
        axes[0,1].set_title('Text Features Std Distribution', fontsize=12, fontweight='bold')
        axes[0,1].set_xlabel('Standard Deviation')
        axes[0,1].set_ylabel('Frequency')
        
    except Exception as e:
        axes[0,0].text(0.5, 0.5, f'Error loading text features:\n{str(e)}', 
                      ha='center', va='center', transform=axes[0,0].transAxes)
        axes[0,1].text(0.5, 0.5, 'Text features not available', 
                      ha='center', va='center', transform=axes[0,1].transAxes)
    
    # 视频特征分析
    try:
        video_feats = np.load(os.path.join(data_path, 'video_feats_final.npy'), allow_pickle=True)
        
        video_mean = np.mean(video_feats, axis=0)
        video_std = np.std(video_feats, axis=0)
        
        axes[1,0].hist(video_mean, bins=50, alpha=0.7, color='green')
        axes[1,0].set_title('Video Features Mean Distribution', fontsize=12, fontweight='bold')
        axes[1,0].set_xlabel('Feature Value')
        axes[1,0].set_ylabel('Frequency')
        
        axes[1,1].hist(video_std, bins=50, alpha=0.7, color='orange')
        axes[1,1].set_title('Video Features Std Distribution', fontsize=12, fontweight='bold')
        axes[1,1].set_xlabel('Standard Deviation')
        axes[1,1].set_ylabel('Frequency')
        
    except Exception as e:
        axes[1,0].text(0.5, 0.5, f'Error loading video features:\n{str(e)}', 
                      ha='center', va='center', transform=axes[1,0].transAxes)
        axes[1,1].text(0.5, 0.5, 'Video features not available', 
                      ha='center', va='center', transform=axes[1,1].transAxes)
    
    plt.tight_layout()
    plt.savefig('feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_data_summary_table(splits, goal_labels, emotion_labels):
    """创建数据摘要表"""
    summary_data = []
    
    for split_name, data in splits.items():
        # 基本统计
        total_samples = len(data)
        
        # 模态统计
        modality_stats = {'text': 0, 'video': 0, 'image': 0, 'audio': 0}
        for sample in data:
            if sample['type'][0]: modality_stats['text'] += 1
            if sample['type'][1]: modality_stats['video'] += 1
            if sample['type'][2]: modality_stats['image'] += 1
            if sample['type'][3]: modality_stats['audio'] += 1
        
        # 标签统计
        goal_counts = []
        emotion_counts = []
        for sample in data:
            goal_counts.extend(sample['goal'])
            emotion_counts.append(sample['emotion'])
        
        unique_goals = len(set(goal_counts))
        unique_emotions = len(set(emotion_counts))
        avg_goals_per_sample = len(goal_counts) / total_samples
        
        summary_data.append({
            'Split': split_name.capitalize(),
            'Total Samples': total_samples,
            'Text Samples': modality_stats['text'],
            'Video Samples': modality_stats['video'],
            'Image Samples': modality_stats['image'],
            'Audio Samples': modality_stats['audio'],
            'Unique Goals': unique_goals,
            'Unique Emotions': unique_emotions,
            'Avg Goals/Sample': f"{avg_goals_per_sample:.2f}"
        })
    
    summary_df = pd.DataFrame(summary_data)
    print("\n=== 数据集摘要表 ===")
    print(summary_df.to_string(index=False))
    
    return summary_df

def main():
    """主函数"""
    print("MINE数据集可视化分析")
    print("=" * 50)
    
    # 加载数据
    splits, goal_labels, emotion_labels = load_data()
    
    # 创建摘要表
    summary_df = create_data_summary_table(splits, goal_labels, emotion_labels)
    
    # 绘制图表
    print("\n正在生成可视化图表...")
    
    # 数据集概览
    plot_dataset_overview(splits)
    
    # 标签分布
    plot_label_distributions(splits, goal_labels, emotion_labels)
    
    # 特征分析
    plot_feature_analysis()
    
    print("\n可视化图表已保存:")
    print("- dataset_overview.png: 数据集概览")
    print("- label_distributions.png: 标签分布")
    print("- feature_analysis.png: 特征分析")

if __name__ == "__main__":
    main()
