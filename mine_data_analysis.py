#!/usr/bin/env python3
"""
MINE数据集完整分析脚本
详细解释项目如何读取和处理多模态数据
"""

import os
import json
import numpy as np
import pandas as pd
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MINEDataAnalyzer:
    """MINE数据集分析器"""
    
    def __init__(self, data_path="CVPR25-MINE/mine-dataset"):
        self.data_path = data_path
        
        # 标签定义 - 来自论文和代码
        self.goal_labels = [
            'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
            'Agree', 'Taunt', 'Flaunt', 'Joke', 'Oppose',
            'Comfort', 'Care', 'Inform', 'Advise', 'Arrange', 
            'Introduce', 'Leave', 'Prevent', 'Greet', 'Ask for help', 'other'
        ]
        
        self.emotion_labels = [
            'Complain', 'Praise', 'Apologise', 'Thank', 'Criticize',
            'Agree', 'Taunt', 'Flaunt', 'Joke', 'Oppose', 'Comfort'
        ]
        
        # 特征维度信息
        self.feature_dims = {
            'text': 768,      # BERT-base-uncased
            'video': 256,     # Video Swin Transformer  
            'image': 256,     # ViT
            'audio': 768      # wav2vec 2.0
        }
        
        # 模态名称映射
        self.modality_names = ['text', 'video', 'image', 'audio']
        
    def load_data_splits(self):
        """加载数据分割"""
        splits = {}
        for split in ['train', 'dev', 'test']:
            file_path = os.path.join(self.data_path, f"{split}_new_update.json")
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    splits[split] = json.load(f)
                print(f"✓ 加载{split}集: {len(splits[split])}个样本")
            else:
                print(f"✗ 未找到文件: {file_path}")
        return splits
    
    def load_features(self):
        """加载预提取的特征"""
        features = {}
        feature_files = {
            'text': 'text_feats_final.npy',
            'video': 'video_feats_final.npy', 
            'image': 'image_feats_final.npy',
            'audio': 'audio_feats_final.npy'
        }
        
        for modality, filename in feature_files.items():
            file_path = os.path.join(self.data_path, filename)
            if os.path.exists(file_path):
                try:
                    feat = np.load(file_path, allow_pickle=True)
                    features[modality] = feat
                    print(f"✓ {modality}特征: 形状{feat.shape}, 类型{feat.dtype}")
                    
                    # 显示第一个特征的详细信息
                    if len(feat) > 0:
                        first_feat = feat[0]
                        if hasattr(first_feat, 'shape'):
                            print(f"  - 单个样本特征维度: {first_feat.shape}")
                        else:
                            print(f"  - 单个样本特征类型: {type(first_feat)}")
                            
                except Exception as e:
                    print(f"✗ 加载{modality}特征失败: {e}")
            else:
                print(f"✗ 未找到特征文件: {file_path}")
        
        return features
    
    def analyze_data_structure(self, splits):
        """分析数据结构"""
        print("\n" + "="*60)
        print("数据结构分析")
        print("="*60)
        
        # 选择一个样本进行详细分析
        if 'train' in splits and len(splits['train']) > 0:
            sample = splits['train'][0]
            print(f"样本结构示例 (ID: {sample['id']}):")
            print(f"  - id: {sample['id']} (样本唯一标识符)")
            print(f"  - type: {sample['type']} (模态可用性: [文本,视频,图像,音频])")
            print(f"  - emotion: {sample['emotion']} -> '{self.emotion_labels[sample['emotion']]}'")
            print(f"  - goal: {sample['goal']} -> {[self.goal_labels[g] for g in sample['goal']]}")
            
            # 解释模态编码
            modalities_present = []
            for i, available in enumerate(sample['type']):
                if available:
                    modalities_present.append(self.modality_names[i])
            print(f"  - 该样本包含的模态: {', '.join(modalities_present)}")
    
    def analyze_modality_combinations(self, splits):
        """分析模态组合分布"""
        print("\n" + "="*60)
        print("模态组合分析")
        print("="*60)
        
        for split_name, data in splits.items():
            print(f"\n{split_name.upper()}集模态组合:")
            
            # 统计模态组合
            combinations = []
            for sample in data:
                combo = tuple(sample['type'])
                combinations.append(combo)
            
            combo_counter = Counter(combinations)
            
            # 显示前10个最常见的组合
            for combo, count in combo_counter.most_common(10):
                modalities = []
                for i, available in enumerate(combo):
                    if available:
                        modalities.append(self.modality_names[i])
                
                if not modalities:
                    combo_str = "无模态"
                else:
                    combo_str = "+".join(modalities)
                
                percentage = (count / len(data)) * 100
                print(f"  {combo_str:20} : {count:4d}个样本 ({percentage:5.1f}%)")
    
    def analyze_label_distribution(self, splits):
        """分析标签分布"""
        print("\n" + "="*60)
        print("标签分布分析")
        print("="*60)
        
        for split_name, data in splits.items():
            print(f"\n{split_name.upper()}集标签分布:")
            
            # 情感标签分布
            emotion_counts = [sample['emotion'] for sample in data]
            emotion_counter = Counter(emotion_counts)
            
            print("  情感标签:")
            for emotion_id, count in emotion_counter.most_common():
                emotion_name = self.emotion_labels[emotion_id]
                percentage = (count / len(data)) * 100
                print(f"    {emotion_name:12} : {count:4d}个样本 ({percentage:5.1f}%)")
            
            # 目标标签分布
            goal_counts = []
            for sample in data:
                goal_counts.extend(sample['goal'])
            
            goal_counter = Counter(goal_counts)
            
            print("  目标标签 (前10):")
            for goal_id, count in goal_counter.most_common(10):
                goal_name = self.goal_labels[goal_id]
                percentage = (count / len(goal_counts)) * 100
                print(f"    {goal_name:12} : {count:4d}次出现 ({percentage:5.1f}%)")
    
    def explain_data_loading_process(self):
        """详细解释数据加载流程"""
        print("\n" + "="*60)
        print("数据加载流程详解")
        print("="*60)
        
        print("""
1. 数据组织架构:
   CVPR25-MINE/mine-dataset/
   ├── train_new_update.json     # 训练集标注 (约16,000样本)
   ├── dev_new_update.json       # 验证集标注 (约2,000样本)  
   ├── test_new_update.json      # 测试集标注 (约2,000样本)
   ├── text_feats_final.npy      # 文本特征 (BERT-768维)
   ├── video_feats_final.npy     # 视频特征 (Video Swin-256维)
   ├── image_feats_final.npy     # 图像特征 (ViT-256维)
   └── audio_feats_final.npy     # 音频特征 (wav2vec-768维)

2. 标注文件格式:
   每个JSON文件包含样本列表，每个样本有:
   - id: 样本唯一标识符 (整数)
   - type: [1,0,1,1] 布尔列表，表示[文本,视频,图像,音频]是否可用
   - emotion: 情感标签ID (0-10，单标签分类)
   - goal: 目标标签ID列表 ([1,5,8]，多标签分类)

3. 特征提取方法:
   - 文本: BERT-base-uncased → 768维向量
   - 视频: Video Swin Transformer → 256维向量
   - 图像: Vision Transformer (ViT) → 256维向量  
   - 音频: wav2vec 2.0 → 768维向量

4. 数据加载流程 (在benchmark_code中):
   a) DataManager类读取JSON标注文件
   b) 根据样本ID加载对应的特征向量
   c) MMDataset类封装多模态数据访问
   d) DataLoader提供批量数据迭代
   e) 处理缺失模态的情况 (用零向量填充)

5. 关键特点:
   - 多模态不完整性: 真实场景中模态经常缺失
   - 多标签分类: 一个样本可能有多个目标意图
   - 情感-意图关联: 情感和意图之间存在内在联系
   - 大规模数据: 超过20,000个社交媒体帖子
        """)
    
    def show_sample_examples(self, splits, features):
        """展示具体样本示例"""
        print("\n" + "="*60)
        print("样本示例展示")
        print("="*60)
        
        if 'train' not in splits:
            print("未找到训练数据")
            return
        
        train_data = splits['train']
        
        # 选择不同模态组合的样本
        examples = []
        seen_combinations = set()
        
        for sample in train_data[:100]:  # 只检查前100个样本
            combo = tuple(sample['type'])
            if combo not in seen_combinations:
                examples.append(sample)
                seen_combinations.add(combo)
                if len(examples) >= 5:  # 最多展示5个不同的组合
                    break
        
        for i, sample in enumerate(examples, 1):
            print(f"\n样本 {i} (ID: {sample['id']}):")
            
            # 模态信息
            available_modalities = []
            for j, available in enumerate(sample['type']):
                if available:
                    available_modalities.append(self.modality_names[j])
            
            print(f"  可用模态: {', '.join(available_modalities) if available_modalities else '无'}")
            
            # 标签信息
            emotion_name = self.emotion_labels[sample['emotion']]
            goal_names = [self.goal_labels[g] for g in sample['goal']]
            
            print(f"  情感标签: {emotion_name}")
            print(f"  目标标签: {', '.join(goal_names)}")
            
            # 特征信息
            sample_idx = int(sample['id']) - 1  # 假设ID从1开始
            print(f"  特征维度:")
            for modality in self.modality_names:
                if modality in features and sample_idx < len(features[modality]):
                    feat = features[modality][sample_idx]
                    if hasattr(feat, 'shape'):
                        print(f"    {modality}: {feat.shape}")
                    else:
                        print(f"    {modality}: {type(feat)}")
                else:
                    print(f"    {modality}: 未加载")
    
    def create_summary_statistics(self, splits):
        """创建汇总统计"""
        print("\n" + "="*60)
        print("数据集统计摘要")
        print("="*60)
        
        total_samples = sum(len(data) for data in splits.values())
        print(f"总样本数: {total_samples:,}")
        
        # 各分割统计
        for split_name, data in splits.items():
            print(f"{split_name.capitalize()}集: {len(data):,}个样本")
        
        # 模态统计
        if 'train' in splits:
            train_data = splits['train']
            modality_counts = [0, 0, 0, 0]  # [text, video, image, audio]
            
            for sample in train_data:
                for i, available in enumerate(sample['type']):
                    if available:
                        modality_counts[i] += 1
            
            print(f"\n训练集模态覆盖:")
            for i, modality in enumerate(self.modality_names):
                count = modality_counts[i]
                percentage = (count / len(train_data)) * 100
                print(f"  {modality.capitalize()}: {count:,}个样本 ({percentage:.1f}%)")
        
        # 标签统计
        print(f"\n标签体系:")
        print(f"  情感标签数: {len(self.emotion_labels)}")
        print(f"  目标标签数: {len(self.goal_labels)}")
        
        # 多标签统计
        if 'train' in splits:
            goal_lengths = [len(sample['goal']) for sample in splits['train']]
            avg_goals = np.mean(goal_lengths)
            print(f"  平均每样本目标数: {avg_goals:.2f}")

def main():
    """主函数"""
    print("MINE数据集完整分析")
    print("="*60)
    
    # 初始化分析器
    analyzer = MINEDataAnalyzer()
    
    # 加载数据
    print("正在加载数据...")
    splits = analyzer.load_data_splits()
    features = analyzer.load_features()
    
    # 执行各种分析
    analyzer.analyze_data_structure(splits)
    analyzer.analyze_modality_combinations(splits)
    analyzer.analyze_label_distribution(splits)
    analyzer.show_sample_examples(splits, features)
    analyzer.create_summary_statistics(splits)
    analyzer.explain_data_loading_process()
    
    print("\n" + "="*60)
    print("分析完成！")
    print("="*60)

if __name__ == "__main__":
    main()
